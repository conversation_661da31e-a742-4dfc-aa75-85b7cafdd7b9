// models/productModel.js
const pool = require('../database');

const productModel = {
  createProduct: async (product) => {
    const {
      item_code, room_id, item_name, car_brand, car_model,
      unit_retail_price, wholesale_price, unit_cost, supplier_code,
      available_stock, location, colour_tape, additional_comments,
      product_category, low_stock_threshold
    } = product;

    const profit = unit_retail_price - unit_cost;

    const query = `
      INSERT INTO products (
        item_code, room_id, item_name, car_brand, car_model,
        unit_retail_price, wholesale_price, unit_cost, supplier_code,
        available_stock, location, colour_tape, profit,
        additional_comments, product_category, low_stock_threshold
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `;

    const values = [
      item_code, room_id, item_name, car_brand, car_model,
      unit_retail_price, wholesale_price, unit_cost, supplier_code,
      available_stock, location, colour_tape, profit,
      additional_comments || '', product_category, low_stock_threshold || 5
    ];

    try {
      const { rows } = await pool.query(query, values);
      return rows[0];
    } catch (error) {
      console.error('Error in createProduct:', error);
      throw error;
    }
  },

  getAllProducts: async () => {
    try {
      const query = `
        SELECT p.*, r.name as room_name
        FROM products p
        JOIN rooms r ON p.room_id = r.id
      `;
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getAllProducts:', error);
      throw error;
    }
  },

  getProductByItemCode: async (item_code) => {
    try {
      const query = `
        SELECT p.*, r.name as room_name
        FROM products p
        JOIN rooms r ON p.room_id = r.id
        WHERE p.item_code = $1
      `;
      const { rows } = await pool.query(query, [item_code]);
      console.log('Products from database:', rows);
      return rows; // Return all products with this item_code (from different rooms)
    } catch (error) {
      console.error('Error in getProductByItemCode:', error);
      throw error;
    }
  },

  getProductByItemCodeAndRoom: async (item_code, room_id) => {
    try {
      const query = `
        SELECT p.*, r.name as room_name
        FROM products p
        JOIN rooms r ON p.room_id = r.id
        WHERE p.item_code = $1 AND p.room_id = $2
      `;
      const { rows } = await pool.query(query, [item_code, room_id]);
      console.log('Product from database:', rows[0]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getProductByItemCodeAndRoom:', error);
      throw error;
    }
  },

  updateProduct: async (item_code, original_room_id, updatedProduct) => {
    const {
      room_id, item_name, car_brand, car_model,
      unit_retail_price, wholesale_price, unit_cost, supplier_code,
      available_stock, location, colour_tape, additional_comments,
      product_category, low_stock_threshold
    } = updatedProduct;

    const profit = unit_retail_price - unit_cost;

    const query = `
      UPDATE products SET
        room_id = $1,
        item_name = $2,
        car_brand = $3,
        car_model = $4,
        unit_retail_price = $5,
        wholesale_price = $6,
        unit_cost = $7,
        supplier_code = $8,
        available_stock = $9,
        location = $10,
        colour_tape = $11,
        profit = $12,
        additional_comments = $13,
        product_category = $14,
        low_stock_threshold = $15
      WHERE item_code = $16 AND room_id = $17
      RETURNING *
    `;

    const values = [
      room_id, item_name, car_brand, car_model,
      unit_retail_price, wholesale_price, unit_cost, supplier_code,
      available_stock, location, colour_tape, profit,
      additional_comments || '', product_category, low_stock_threshold || 5, item_code, original_room_id
    ];

    try {
      const { rows } = await pool.query(query, values);
      console.log('Updated product result:', rows[0]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error in updateProduct:', error);
      throw error;
    }
  },

  deleteProduct: async (item_code, room_id) => {
    try {
      const query = 'DELETE FROM products WHERE item_code = $1 AND room_id = $2 RETURNING *';
      const { rows } = await pool.query(query, [item_code, room_id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteProduct:', error);
      throw error;
    }
  },

  getProductsByRoomId: async (room_id) => {
    try {
      const query = `
        SELECT p.*, r.name as room_name
        FROM products p
        JOIN rooms r ON p.room_id = r.id
        WHERE p.room_id = $1
      `;
      const { rows } = await pool.query(query, [room_id]);
      return rows;
    } catch (error) {
      console.error('Error in getProductsByRoomId:', error);
      throw error;
    }
  },

  deleteProductsByRoomId: async (room_id) => {
    try {
      const query = 'DELETE FROM products WHERE room_id = $1 RETURNING *';
      const { rows } = await pool.query(query, [room_id]);
      return rows.length;
    } catch (error) {
      console.error('Error in deleteProductsByRoomId:', error);
      throw error;
    }
  },

  updateProductStock: async (item_code, room_id, newStock) => {
    try {
      const query = 'UPDATE products SET available_stock = $1 WHERE item_code = $2 AND room_id = $3 RETURNING *';
      const { rows } = await pool.query(query, [newStock, item_code, room_id]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in updateProductStock:', error);
      throw error;
    }
  },

  getLowStockProducts: async () => {
    try {
      const query = `
        SELECT p.*, r.name as room_name
        FROM products p
        JOIN rooms r ON p.room_id = r.id
        WHERE p.available_stock <= p.low_stock_threshold
        ORDER BY p.available_stock ASC
      `;
      const { rows } = await pool.query(query);
      return rows;
    } catch (error) {
      console.error('Error in getLowStockProducts:', error);
      throw error;
    }
  }
};

module.exports = productModel;